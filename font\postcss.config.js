export default {
  plugins: {
    "postcss-pxtorem": {
      // 根字体大小，基于设计稿375px，设置为37.5px
      // 这样设计稿上的375px = 10rem，方便计算
      rootValue: 37.5,

      // 需要转换的CSS属性，* 表示所有属性
      propList: ["*"],

      // 不需要转换的选择器，可以用正则表达式
      selectorBlackList: [
        ".no-rem", // 添加.no-rem类名的元素不转换
        /^\.van-/ // vant组件不转换，避免样式冲突
      ],

      // 替换包含rem的规则，而不是添加备用规则
      replace: true,

      // 允许在媒体查询中转换px
      mediaQuery: false,

      // 设置最小的转换数值，小于这个值的px不会被转换
      minPixelValue: 1,

      // 排除的文件夹或文件
      exclude: /node_modules/i,

      // 转换后保留的小数位数
      unitPrecision: 5
    }
  }
};
