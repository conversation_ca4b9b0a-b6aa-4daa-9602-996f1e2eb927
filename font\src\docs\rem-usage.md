# H5移动端rem适配使用说明

## 配置说明

本项目已配置基于设计稿375px的rem适配方案，包含以下配置：

### 1. PostCSS配置 (postcss.config.js)

- 根字体大小：37.5px（375px设计稿 ÷ 10 = 37.5px）
- 自动转换所有px为rem
- 排除vant组件和node_modules
- 支持.no-rem类名跳过转换

### 2. 动态适配脚本 (src/utils/rem.ts)

- 根据屏幕宽度动态调整根字体大小
- 支持横竖屏切换
- 限制最小宽度320px，最大宽度750px

## 使用方法

### 1. 直接写px，自动转换为rem

```css
/* 你写的CSS */
.container {
  width: 375px; /* 设计稿全宽 */
  height: 200px; /* 设计稿高度 */
  font-size: 16px; /* 字体大小 */
  margin: 10px; /* 间距 */
}

/* 编译后的CSS */
.container {
  width: 10rem; /* 375px ÷ 37.5 = 10rem */
  height: 5.33333rem; /* 200px ÷ 37.5 = 5.33333rem */
  font-size: 0.42667rem; /* 16px ÷ 37.5 = 0.42667rem */
  margin: 0.26667rem; /* 10px ÷ 37.5 = 0.26667rem */
}
```

### 2. 跳过rem转换

如果某些元素不需要rem转换，可以使用`.no-rem`类名：

```css
.no-rem {
  border: 1px solid #ccc; /* 这里的1px不会被转换 */
}
```

### 3. 在JavaScript中使用

```typescript
import { pxToRem, remToPx } from "@/utils/rem";

// px转rem
const remValue = pxToRem(100); // 100px转为rem

// rem转px（基于当前屏幕的实际字体大小）
const pxValue = remToPx(2); // 2rem转为px
```

## 适配效果

### 不同屏幕宽度下的根字体大小：

| 屏幕宽度 | 根字体大小 | 说明          |
| -------- | ---------- | ------------- |
| 320px    | 32px       | 最小宽度限制  |
| 375px    | 37.5px     | 设计稿标准    |
| 414px    | 41.4px     | iPhone 6 Plus |
| 750px    | 75px       | 最大宽度限制  |

### 计算公式：

```
根字体大小 = (当前屏幕宽度 / 设计稿宽度) × 基础字体大小
根字体大小 = (当前屏幕宽度 / 375) × 37.5
```

## 注意事项

1. **vant组件样式**：vant组件的样式不会被转换，避免样式冲突
2. **1px边框**：建议使用vant的hairline方案处理1px边框问题
3. **字体大小**：建议最小字体不要小于12px（约0.32rem）
4. **调试模式**：开发环境下会在控制台输出适配信息

## 示例页面

```vue
<template>
  <div class="demo-page">
    <div class="header">
      <h1>标题</h1>
    </div>
    <div class="content">
      <div class="card">
        <p>这是一个卡片</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  width: 375px; /* 全屏宽度，会转换为10rem */
  padding: 20px; /* 会转换为0.53333rem */
}

.header {
  height: 60px; /* 会转换为1.6rem */
  background: #f5f5f5;
}

.header h1 {
  font-size: 18px; /* 会转换为0.48rem */
  line-height: 60px; /* 会转换为1.6rem */
}

.content {
  margin-top: 20px; /* 会转换为0.53333rem */
}

.card {
  padding: 15px; /* 会转换为0.4rem */
  background: white;
  border-radius: 8px; /* 会转换为0.21333rem */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 不需要转换的样式 */
.no-rem {
  border: 1px solid #ddd; /* 1px不会被转换 */
}
</style>
```

这样配置后，你就可以直接按照375px的设计稿写px值，系统会自动转换为rem并适配不同屏幕尺寸。

## 测试页面

访问 `/rem-test` 路由可以查看rem适配的实际效果，包括：

- 当前屏幕信息显示
- 不同尺寸元素的适配效果
- rem转换与固定px的对比
- 实时响应屏幕尺寸变化

## 实际项目应用

在你的现有项目中，只需要：

1. **继续按设计稿写CSS**：直接使用375px设计稿上的尺寸值
2. **自动转换**：构建时会自动将px转换为rem
3. **响应式适配**：不同设备会自动缩放到合适大小

### 示例：

```css
/* 设计稿上的卡片 */
.card {
  width: 335px; /* 会转换为 8.93333rem */
  height: 200px; /* 会转换为 5.33333rem */
  padding: 20px; /* 会转换为 0.53333rem */
  margin: 15px; /* 会转换为 0.4rem */
  border-radius: 8px; /* 会转换为 0.21333rem */
}

/* 字体大小 */
.title {
  font-size: 18px; /* 会转换为 0.48rem */
}

.content {
  font-size: 14px; /* 会转换为 0.37333rem */
}
```

这样你就实现了类似微信小程序rpx的效果，可以轻松适配不同尺寸的手机屏幕！
