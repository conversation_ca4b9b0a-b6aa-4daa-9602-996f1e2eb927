/**
 * H5移动端rem适配工具
 * 基于设计稿375px进行适配
 */

// 设计稿宽度
const DESIGN_WIDTH = 375;

// 基础字体大小（与postcss配置中的rootValue保持一致）
const BASE_FONT_SIZE = 37.5;

/**
 * 设置根字体大小
 */
function setRootFontSize(): void {
  const deviceWidth = document.documentElement.clientWidth || window.innerWidth;
  
  // 限制最小和最大宽度，避免在极端屏幕尺寸下显示异常
  const minWidth = 320; // 最小宽度
  const maxWidth = 750; // 最大宽度，可以根据需要调整
  
  const width = Math.max(minWidth, Math.min(maxWidth, deviceWidth));
  
  // 计算当前设备应该设置的字体大小
  const fontSize = (width / DESIGN_WIDTH) * BASE_FONT_SIZE;
  
  // 设置根元素字体大小
  document.documentElement.style.fontSize = fontSize + 'px';
  
  // 在控制台输出调试信息（生产环境可以移除）
  if (process.env.NODE_ENV === 'development') {
    console.log(`设备宽度: ${deviceWidth}px, 计算宽度: ${width}px, 根字体大小: ${fontSize}px`);
  }
}

/**
 * 初始化rem适配
 */
export function initRem(): void {
  // 页面加载时设置
  setRootFontSize();
  
  // 监听窗口大小变化
  let resizeTimer: NodeJS.Timeout;
  window.addEventListener('resize', () => {
    // 防抖处理，避免频繁触发
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
      setRootFontSize();
    }, 100);
  });
  
  // 监听设备方向变化
  window.addEventListener('orientationchange', () => {
    // 延迟执行，等待设备方向变化完成
    setTimeout(() => {
      setRootFontSize();
    }, 300);
  });
}

/**
 * px转rem的工具函数（可选，用于JS中的计算）
 * @param px 像素值
 * @returns rem值
 */
export function pxToRem(px: number): number {
  return px / BASE_FONT_SIZE;
}

/**
 * rem转px的工具函数（可选，用于JS中的计算）
 * @param rem rem值
 * @returns 像素值
 */
export function remToPx(rem: number): number {
  const currentFontSize = parseFloat(document.documentElement.style.fontSize) || BASE_FONT_SIZE;
  return rem * currentFontSize;
}
