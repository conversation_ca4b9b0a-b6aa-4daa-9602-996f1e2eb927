<template>
  <div class="rem-test-page">
    <div class="header">
      <h1>Rem适配测试页面</h1>
      <p>设计稿基准：375px</p>
    </div>
    
    <div class="info-section">
      <div class="info-card">
        <h3>当前屏幕信息</h3>
        <p>屏幕宽度: {{ screenWidth }}px</p>
        <p>根字体大小: {{ rootFontSize }}px</p>
        <p>适配比例: {{ adaptRatio.toFixed(3) }}</p>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试元素</h2>
      
      <div class="test-box">
        <p>这个盒子宽度是 300px (8rem)</p>
        <p>高度是 150px (4rem)</p>
        <p>字体大小是 16px (0.42667rem)</p>
      </div>
      
      <div class="test-grid">
        <div class="grid-item">75px<br/>(2rem)</div>
        <div class="grid-item">75px<br/>(2rem)</div>
        <div class="grid-item">75px<br/>(2rem)</div>
        <div class="grid-item">75px<br/>(2rem)</div>
      </div>
      
      <div class="button-group">
        <button class="test-btn primary">主要按钮 (120px宽)</button>
        <button class="test-btn secondary">次要按钮 (100px宽)</button>
      </div>
    </div>
    
    <div class="no-rem-section">
      <h2>不转换rem的元素</h2>
      <div class="no-rem test-border">
        <p>这个元素使用了 .no-rem 类，边框是 1px 不会被转换</p>
      </div>
    </div>
    
    <div class="comparison-section">
      <h2>对比测试</h2>
      <div class="comparison-grid">
        <div class="comparison-item">
          <h4>使用rem (会适配)</h4>
          <div class="rem-box">100px × 100px</div>
        </div>
        <div class="comparison-item">
          <h4>固定px (不适配)</h4>
          <div class="px-box no-rem">100px × 100px</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const screenWidth = ref(0)
const rootFontSize = ref(0)
const adaptRatio = ref(0)

const updateScreenInfo = () => {
  screenWidth.value = window.innerWidth
  const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
  rootFontSize.value = fontSize
  adaptRatio.value = fontSize / 37.5 // 基准字体大小
}

onMounted(() => {
  updateScreenInfo()
  window.addEventListener('resize', updateScreenInfo)
  window.addEventListener('orientationchange', () => {
    setTimeout(updateScreenInfo, 300)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenInfo)
  window.removeEventListener('orientationchange', updateScreenInfo)
})
</script>

<style scoped>
.rem-test-page {
  padding: 20px;
  max-width: 375px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header h1 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.header p {
  font-size: 14px;
  color: #666;
}

.info-section {
  margin-bottom: 30px;
}

.info-card {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.info-card h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #1976d2;
}

.info-card p {
  font-size: 14px;
  margin: 5px 0;
  color: #333;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.test-box {
  width: 300px;
  height: 150px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.test-box p {
  font-size: 16px;
  margin: 5px 0;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(4, 75px);
  gap: 10px;
  margin-bottom: 20px;
}

.grid-item {
  width: 75px;
  height: 75px;
  background: #ff9800;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.test-btn {
  height: 40px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.test-btn.primary {
  width: 120px;
  background: #4caf50;
  color: white;
}

.test-btn.secondary {
  width: 100px;
  background: #2196f3;
  color: white;
}

.test-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.no-rem-section {
  margin-bottom: 30px;
}

.no-rem-section h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.test-border {
  border: 1px solid #e91e63;
  padding: 15px;
  border-radius: 8px;
  background: white;
}

.comparison-section h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.comparison-item {
  text-align: center;
}

.comparison-item h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
}

.rem-box {
  width: 100px;
  height: 100px;
  background: #9c27b0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 12px;
  margin: 0 auto;
}

.px-box {
  width: 100px !important;
  height: 100px !important;
  background: #f44336;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 12px;
  margin: 0 auto;
}
</style>
