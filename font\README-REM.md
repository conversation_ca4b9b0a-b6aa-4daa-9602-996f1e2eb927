# H5移动端Rem适配方案

## 概述

本项目已配置完成基于375px设计稿的rem适配方案，实现类似微信小程序rpx的效果。你可以直接按照设计稿尺寸写px值，系统会自动转换为rem并适配不同屏幕尺寸。

## 配置文件

### 1. PostCSS配置 (`postcss.config.js`)
```javascript
export default {
  plugins: {
    "postcss-pxtorem": {
      rootValue: 37.5,        // 根字体大小 (375px ÷ 10)
      propList: ["*"],        // 转换所有属性
      selectorBlackList: [    // 排除转换的选择器
        ".no-rem",           // .no-rem类不转换
        /^\.van-/            // vant组件不转换
      ],
      replace: true,
      mediaQuery: false,
      minPixelValue: 1,       // 最小转换值
      exclude: /node_modules/i,
      unitPrecision: 5        // 小数位数
    }
  }
}
```

### 2. 动态适配脚本 (`src/utils/rem.ts`)
- 根据屏幕宽度动态设置根字体大小
- 支持横竖屏切换
- 限制最小宽度320px，最大宽度750px
- 提供px转rem和rem转px的工具函数

### 3. 基础样式 (`src/styles/mobile-base.css`)
- 移动端基础样式重置
- 常用布局、间距、字体大小类
- 组件样式（按钮、表单、列表等）
- 所有尺寸都会自动转换为rem

## 使用方法

### 1. 直接写px，自动转换
```css
.my-component {
  width: 335px;      /* 转换为 8.93333rem */
  height: 200px;     /* 转换为 5.33333rem */
  padding: 20px;     /* 转换为 0.53333rem */
  font-size: 16px;   /* 转换为 0.42667rem */
  border-radius: 8px; /* 转换为 0.21333rem */
}
```

### 2. 使用基础样式类
```html
<div class="container p-20 mt-15">
  <div class="card">
    <h3 class="text-18 mb-10">标题</h3>
    <p class="text-14 text-gray">内容</p>
    <button class="btn btn-primary mt-15">按钮</button>
  </div>
</div>
```

### 3. 跳过rem转换
```css
.no-rem {
  border: 1px solid #ddd; /* 1px不会被转换 */
}

.hairline.no-rem {
  border: 0.5px solid #e0e0e0; /* 用于1px边框问题 */
}
```

### 4. JavaScript中使用
```typescript
import { pxToRem, remToPx } from '@/utils/rem';

// px转rem
const remValue = pxToRem(100); // 100px转为rem

// rem转px（基于当前屏幕）
const pxValue = remToPx(2); // 2rem转为px
```

## 适配效果

| 屏幕宽度 | 根字体大小 | 适配比例 | 说明 |
|---------|-----------|---------|------|
| 320px   | 32px      | 0.853   | 最小宽度 |
| 375px   | 37.5px    | 1.000   | 设计稿标准 |
| 414px   | 41.4px    | 1.104   | iPhone 6 Plus |
| 750px   | 75px      | 2.000   | 最大宽度 |

## 测试页面

访问 `/rem-test` 可以查看适配效果：
- 实时显示屏幕信息
- 不同尺寸元素的适配效果
- rem转换与固定px的对比
- 响应式效果演示

## 注意事项

1. **vant组件**：vant组件样式不会被转换，避免样式冲突
2. **1px边框**：使用`.no-rem`类或hairline方案处理1px边框
3. **字体大小**：建议最小字体不小于12px（约0.32rem）
4. **调试模式**：开发环境会在控制台输出适配信息

## 与微信小程序rpx对比

| 特性 | 微信小程序rpx | 本方案rem |
|-----|-------------|----------|
| 设计稿基准 | 750px | 375px |
| 单位换算 | 1rpx = 0.5px (在375px设备) | 1px = 0.02667rem (在375px设备) |
| 自动转换 | 是 | 是 |
| 响应式 | 是 | 是 |
| 兼容性 | 仅小程序 | 所有现代浏览器 |

## 迁移指南

如果你有现有的CSS代码需要适配：

1. **保持原有px值**：不需要修改，会自动转换
2. **添加.no-rem类**：对于不需要适配的元素
3. **使用基础样式类**：可以减少重复代码
4. **测试不同屏幕**：使用浏览器开发者工具测试

## 常见问题

### Q: 为什么选择375px作为设计稿基准？
A: 375px是iPhone 6/7/8的屏幕宽度，是目前最常见的移动设备尺寸，大部分UI设计师也使用这个尺寸作为设计稿标准。

### Q: 如何处理1px边框问题？
A: 使用`.no-rem`类跳过转换，或使用提供的hairline样式。

### Q: 可以修改根字体大小吗？
A: 可以，但需要同时修改`postcss.config.js`中的`rootValue`和`src/utils/rem.ts`中的`BASE_FONT_SIZE`。

### Q: 如何在现有项目中使用？
A: 直接按照设计稿写px值即可，系统会自动转换。对于不需要适配的元素添加`.no-rem`类。

## 总结

通过这套rem适配方案，你可以：
- ✅ 像写微信小程序一样直接使用设计稿尺寸
- ✅ 自动适配不同屏幕尺寸
- ✅ 保持代码简洁，无需手动计算
- ✅ 兼容所有现代浏览器
- ✅ 支持响应式设计

现在你可以愉快地开发H5移动端页面了！🎉
