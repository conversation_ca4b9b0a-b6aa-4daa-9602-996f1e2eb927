import Login from "@/views/user/login/index.vue";
import Register from "@/views/user/login/register.vue";
import Home from "@/views/home/<USER>";
import User from "@/views/user/index.vue";
import Setting from "@/views/setting/index.vue";
import type { RouteRecordRaw } from "vue-router";
import Layout from "@/layout/index.vue";
// import Blank from "@/layout/blank.vue";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "Root",
    component: Layout,
    redirect: { name: "Home" },
    children: [
      {
        path: "login",
        name: "Login",
        component: Login,
        meta: {
          noTab: true
        }
      },
      {
        path: "register",
        name: "Register",
        component: Register,
        meta: {
          noTab: true
        }
      },
      {
        path: "password/reset",
        name: "PasswordReset",
        component: () => import("@/views/user/login/resetPassword.vue"),
        meta: {
          noTab: true
        }
      },
      {
        path: "home",
        name: "Home",
        component: Home,
        meta: {
          title: "主页"
        }
      },
      {
        path: "/profile",
        name: "Profile",
        redirect: { name: "ProfileIndex" },
        meta: {
          title: "我的档案"
        },
        children: [
          {
            path: "index",
            name: "ProfileIndex",
            component: () => import("@/views/profile/list/index.vue"),
            meta: {
              title: "档案列表",
              noTab: true
            }
          },
          {
            path: "create",
            name: "CreateProfile",
            component: () => import("@/views/profile/edit/index.vue"),
            meta: {
              title: "新增档案",
              noTab: true
            }
          },
          {
            path: "edit",
            name: "EditProfile",
            component: () => import("@/views/profile/edit/index.vue"),
            meta: {
              title: "编辑档案",
              noTab: true
            }
          }
        ]
      },
      {
        path: "/paipan",
        name: "Paipan",
        redirect: { name: "createPaipan" },
        meta: {
          title: "排盘",
          noTab: true
        },
        children: [
          {
            path: "create",
            name: "createPaipan",
            component: () => import("@/views/paipan/main/index.vue"),
            meta: {
              title: "八字排盘"
            }
          },
          {
            path: "edit",
            name: "editPaipan",
            component: () => import("@/views/paipan/main/index.vue"),
            meta: {
              title: "八字排盘"
            }
          },
          {
            path: "result",
            name: "BaziResult",
            component: () => import("@/views/paipan/result/index.vue"),
            meta: {
              title: "我的八字",
              noTab: true
            }
          }
        ]
      },
      {
        path: "/message",
        name: "Message",
        redirect: { name: "MessageIndex" },
        meta: {
          title: "消息"
        },
        children: [
          {
            path: "index",
            name: "MessageIndex",
            component: () => import("@/views/message/main/index.vue"),
            meta: {
              title: "消息"
            }
          },
          {
            path: "system",
            name: "SystemMessage",
            component: () => import("@/views/message/system/index.vue"),
            meta: {
              title: "系统消息",
              noTab: true
            }
          },
          {
            path: "activity",
            name: "ActivityMessage",
            component: () => import("@/views/message/activity/index.vue"),
            meta: {
              title: "活动公告",
              noTab: true
            }
          },
          {
            path: "publish",
            name: "PublishDiscussion",
            component: () => import("@/views/message/publish/index.vue"),
            meta: {
              title: "发布",
              noTab: true
            }
          },
          {
            path: "draft",
            name: "DiscussionDraft",
            component: () => import("@/views/message/draft/index.vue"),
            meta: {
              title: "草稿箱",
              noTab: true
            }
          },
          {
            path: "detail",
            name: "DiscussionDetail",
            component: () => import("@/views/message/discussion/index.vue"),
            meta: {
              title: "帖子详情",
              noTab: true
            }
          }
        ]
      },
      {
        path: "/user",
        name: "User",
        component: User,
        meta: {
          title: "我的"
        }
      },
      {
        path: "/contactUs",
        name: "ContactUs",
        component: () => import("@/views/user/main/contactUs.vue"),
        meta: {
          title: "联系我们",
          noTab: true
        }
      },
      {
        path: "/browsingHistory",
        name: "BrowsingHistory",
        component: () => import("@/views/user/browsing-history/index.vue"),
        meta: {
          title: "浏览记录",
          noTab: true
        }
      },
      {
        path: "/setting",
        name: "Setting",
        component: Setting,
        meta: {
          title: "设置",
          noTab: true
        }
      },
      {
        path: "/rem-test",
        name: "RemTest",
        component: () => import("@/views/RemTestPage.vue"),
        meta: {
          title: "Rem适配测试",
          noTab: true
        }
      }
    ]
  }
];

export default routes;
