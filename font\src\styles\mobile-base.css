/**
 * 移动端基础样式
 * 基于375px设计稿，使用rem适配
 */

/* 重置默认样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px; /* 会转换为 0.37333rem */
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 常用布局类 */
.container {
  max-width: 375px; /* 会转换为 10rem */
  margin: 0 auto;
  padding: 0 15px; /* 会转换为 0.4rem */
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 间距类 */
.mt-10 { margin-top: 10px; } /* 会转换为 0.26667rem */
.mt-15 { margin-top: 15px; } /* 会转换为 0.4rem */
.mt-20 { margin-top: 20px; } /* 会转换为 0.53333rem */
.mt-30 { margin-top: 30px; } /* 会转换为 0.8rem */

.mb-10 { margin-bottom: 10px; }
.mb-15 { margin-bottom: 15px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.ml-10 { margin-left: 10px; }
.ml-15 { margin-left: 15px; }
.mr-10 { margin-right: 10px; }
.mr-15 { margin-right: 15px; }

.p-10 { padding: 10px; }
.p-15 { padding: 15px; }
.p-20 { padding: 20px; }

.px-15 { padding-left: 15px; padding-right: 15px; }
.py-10 { padding-top: 10px; padding-bottom: 10px; }
.py-15 { padding-top: 15px; padding-bottom: 15px; }

/* 字体大小类 */
.text-12 { font-size: 12px; } /* 会转换为 0.32rem */
.text-14 { font-size: 14px; } /* 会转换为 0.37333rem */
.text-16 { font-size: 16px; } /* 会转换为 0.42667rem */
.text-18 { font-size: 18px; } /* 会转换为 0.48rem */
.text-20 { font-size: 20px; } /* 会转换为 0.53333rem */
.text-24 { font-size: 24px; } /* 会转换为 0.64rem */

/* 颜色类 */
.text-primary { color: #1976d2; }
.text-success { color: #4caf50; }
.text-warning { color: #ff9800; }
.text-danger { color: #f44336; }
.text-gray { color: #666; }
.text-light-gray { color: #999; }

/* 背景色类 */
.bg-white { background-color: #fff; }
.bg-gray { background-color: #f5f5f5; }
.bg-primary { background-color: #1976d2; }
.bg-success { background-color: #4caf50; }

/* 常用组件样式 */
.card {
  background: white;
  border-radius: 8px; /* 会转换为 0.21333rem */
  padding: 15px; /* 会转换为 0.4rem */
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px; /* 会转换为 0.32rem 0.64rem */
  border: none;
  border-radius: 6px; /* 会转换为 0.16rem */
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.btn-large {
  padding: 15px 30px; /* 会转换为 0.4rem 0.8rem */
  font-size: 16px;
}

.btn-small {
  padding: 8px 16px; /* 会转换为 0.21333rem 0.42667rem */
  font-size: 12px;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20px; /* 会转换为 0.53333rem */
}

.form-label {
  display: block;
  margin-bottom: 8px; /* 会转换为 0.21333rem */
  font-size: 14px;
  color: #333;
}

.form-input {
  width: 100%;
  height: 44px; /* 会转换为 1.17333rem */
  padding: 0 15px; /* 会转换为 0 0.4rem */
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: #1976d2;
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 15px; /* 会转换为 0.4rem */
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
  margin-left: 12px; /* 会转换为 0.32rem */
}

.list-item-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px; /* 会转换为 0.10667rem */
}

.list-item-desc {
  font-size: 12px;
  color: #999;
}

/* 头部样式 */
.header {
  height: 44px; /* 会转换为 1.17333rem */
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 底部导航样式 */
.tabbar {
  height: 50px; /* 会转换为 1.33333rem */
  background: white;
  border-top: 1px solid #f0f0f0;
  display: flex;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 10px; /* 会转换为 0.26667rem */
  color: #999;
}

.tabbar-item.active {
  color: #1976d2;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: 4px; } /* 会转换为 0.10667rem */
.rounded-lg { border-radius: 8px; } /* 会转换为 0.21333rem */

/* 不转换rem的样式 */
.no-rem {
  /* 这些样式不会被转换为rem */
}

.hairline {
  border: 0.5px solid #e0e0e0; /* 细线边框，不转换 */
}

.hairline.no-rem::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  border: 1px solid #e0e0e0;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
}
